{"name": "fluttertoast", "version": "0.0.2", "summary": "Toast Library for Flutter", "description": "Toast Library for FLutter", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"Karthik Ponnam": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "Toast": []}, "pod_target_xcconfig": {"VALID_ARCHS": "x86_64 armv7 arm64", "DEFINES_MODULE": "YES"}, "platforms": {"osx": null, "ios": null, "tvos": null, "visionos": null, "watchos": null}}