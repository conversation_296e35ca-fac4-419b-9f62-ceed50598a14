{"name": "monnify_payment_sdk", "version": "1.0.0", "summary": "Flutter plugin for monnify sdk.", "description": "Allows for making payments through the monnify system.", "homepage": "http://monnify.com", "license": {"file": "../LICENSE"}, "authors": {"Monnify LTD": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "MonnifyiOSSDK": ["~> 0.2.6"]}, "platforms": {"ios": "13.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "swift_versions": "5.0", "swift_version": "5.0"}