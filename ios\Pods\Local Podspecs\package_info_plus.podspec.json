{"name": "package_info_plus", "version": "0.4.5", "summary": "Flutter Package Info", "description": "This Flutter plugin provides an API for querying information about an application package.\nDownloaded by pub (not CocoaPods).", "homepage": "https://github.com/fluttercommunity/plus_plugins", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Community Team": "<EMAIL>"}, "source": {"http": "https://github.com/fluttercommunity/plus_plugins/tree/main/packages/sensors_plus"}, "documentation_url": "https://pub.dev/packages/sensors_plus", "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "platforms": {"ios": "11.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}}