{"name": "file_picker", "version": "0.0.1", "summary": "A flutter plugin to show native file picker dialogs", "description": "A flutter plugin to show native file picker dialogs.", "homepage": "https://github.com/miguelpruivo/plugins_flutter_file_picker", "license": {"file": "../LICENSE"}, "authors": "<PERSON>", "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "platforms": {"ios": "8.0"}, "dependencies": {"Flutter": [], "DKImagePickerController/PhotoGallery": []}, "pod_target_xcconfig": {"GCC_PREPROCESSOR_DEFINITIONS": "PICKER_MEDIA=1 PICKER_AUDIO=1 PICKER_DOCUMENT=1"}}