{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\beremit\\beremit Mobile\\android\\app\\.cxx\\RelWithDebInfo\\676v6a4b\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\beremit\\beremit Mobile\\android\\app\\.cxx\\RelWithDebInfo\\676v6a4b\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}