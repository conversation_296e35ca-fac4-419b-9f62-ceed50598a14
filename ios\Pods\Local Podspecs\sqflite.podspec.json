{"name": "sqflite", "version": "0.0.3", "summary": "SQLite plugin.", "description": "Accss SQLite database.", "homepage": "https://github.com/tekartik/sqflite", "license": {"file": "../LICENSE"}, "authors": {"Tekartik": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "FMDB": [">= 2.7.5"]}, "platforms": {"ios": "11.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "x86_64"}}