{"name": "pusher_channels_flutter", "version": "0.0.1", "summary": "Pusher Channels Flutter integration.", "description": "A new flutter plugin project.", "homepage": "https://github.com/pusher/pusher-channels-flutter", "license": {"file": "../LICENSE"}, "authors": {"Pusher": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"Flutter": [], "PusherSwift": ["~> 10.1.4"]}, "platforms": {"ios": "13.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "swift_versions": "5.0", "swift_version": "5.0"}