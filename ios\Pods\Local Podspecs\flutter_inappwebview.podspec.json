{"name": "flutter_inappwebview", "version": "0.0.1", "summary": "A new Flutter plugin.", "description": "A new Flutter plugin.", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"Your Company": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "resources": "Storyboards/**/*.storyboard", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "OrderedSet": ["~>5.0"]}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "x86_64"}, "swift_versions": "5.0", "platforms": {"ios": "11.0"}, "default_subspecs": "Core", "subspecs": [{"name": "Core", "platforms": {"ios": "8.0"}}], "swift_version": "5.0"}