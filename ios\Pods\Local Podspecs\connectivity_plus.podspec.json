{"name": "connectivity_plus", "version": "0.0.1", "summary": "Flutter Connectivity", "description": "This plugin allows Flutter apps to discover network connectivity and configure themselves accordingly.\nDownloaded by pub (not CocoaPods).", "homepage": "https://plus.fluttercommunity.dev/", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Community Team": "<EMAIL>"}, "source": {"http": "https://github.com/fluttercommunity/plus_plugins/tree/main/packages/connectivity_plus"}, "documentation_url": "https://pub.dev/packages/connectivity_plus", "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": [], "ReachabilitySwift": []}, "platforms": {"ios": "12.0"}, "swift_versions": "5.0", "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_version": "5.0"}