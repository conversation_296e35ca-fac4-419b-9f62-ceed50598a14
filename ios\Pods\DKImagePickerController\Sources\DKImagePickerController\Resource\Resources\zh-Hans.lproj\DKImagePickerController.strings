//
//  GitHub
//  https://github.com/zhangao0086/DKImagePickerController
//
//
//  License
//  Copyright (c) 2014 ZhangAo
//  Released under an MIT license: http://opensource.org/licenses/MIT
//

"permission.camera.title" = "请授权使用相机";

"permission.photo.title" = "请授权使用照片";

"permission.allow" = "允许访问";

"picker.alert.ok" = "确定";

"picker.select.title" = "选择(%@)";

"picker.select.done.title" = "完成";

"picker.select.all.title" = "全选";

"picker.select.photosOrVideos.error.title" = "选择照片或视频";

"picker.select.photosOrVideos.error.message" = "不能同时选中照片或视频.";

"picker.select.maxLimitReached.error.title" = "最大数限制";

"picker.select.maxLimitReached.error.message" = "你能选择%@项";
